package com.inossem.wms.starter.setting;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.google.common.base.Function;
import com.google.common.base.Optional;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.common.base.BizContext;

import lombok.extern.slf4j.Slf4j;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.RequestHandler;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.RequestParameterBuilder;
import springfox.documentation.schema.Example;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.ParameterType;
import springfox.documentation.service.RequestParameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 *         <p>
 *         访问地址 http://127.0.0.1:8080/wms/web/doc.html
 */
@EnableSwagger2
@Configuration
@Import(BeanValidatorPluginsConfiguration.class)
@Slf4j
public class WmsSwaggerConfig {

    @Value("${springfox.documentation.enabled:false}")
    private Boolean enabled;

    public static Predicate<RequestHandler> basePackage(final String basePackage) {
//        log.info("Swagger API basePackage 初始化..");
        return input -> declaringClass(input).transform(handlerPackage(basePackage)).or(true);
    }

    private static Function<Class<?>, Boolean> handlerPackage(final String basePackage) {
        return input -> {
            // 循环判断匹配
            for (String strPackage : basePackage.split(",")) {
                boolean isMatch = input.getPackage().getName().startsWith(strPackage);
                if (isMatch) {
                    return true;
                }
            }
            return false;
        };
    }

    private static Optional<? extends Class<?>> declaringClass(RequestHandler input) {
        return Optional.fromNullable(input.declaringClass());
    }

    private ApiInfo apiInfo() {
//        log.info("Swagger API 初始化..");
        return new ApiInfoBuilder()
                .title("智能仓储项目API文档")
                .description("智能仓储项目的接口文档，仅限内部使用")
                .termsOfServiceUrl("http://www.inossem.com")
                .contact(new Contact("英诺森", "", ""))
                .version("3.0")
                .build();
    }


    /**
     * swagger 接口 请求头加 token
     *
     * @date 2021/3/9 16:34
     * <AUTHOR>
     */
    public List<RequestParameter> requestParameter() {
        List<RequestParameter> parameters = new ArrayList<>();
        // 请求头token
        parameters.add(new RequestParameterBuilder()
                .name("Authorization")
                .in(ParameterType.HEADER)
                .description("token")
                .required(true)
                .example(new Example(
                        "eyJhbGciOiJIUzUxMiIsInppcCI6IkdaSVAifQ.H4sIAAAAAAAAAHVUTXOUQBD9L5xzmOmZno-9RddDyrhlxXhyPbDQREpgKGBVTOW_28NX1miqKOD1m-5-M_3gMenPp2SXpHldNslVcu6pe08jR6SRiFKDBWlxYTj8eEyyjtKBPjO-yY_JTlwdk7L_NDb8Lvm9DnlZjBsdQ_3Y31EVQ3ehotuyH5j48v9Sc_p9WVNMjiIsq_BCRHJe_wrZce2tZQRvQx7XHefdHZN_xU3apxSN0qBSEoxFhxw-bxt4-srwR1qVeTmM75p8zxKm9lp4wzcxt48JW8fr_Yebw9SR6rYKIz2LmaId9eHcZax3PQ2ptXXaaLQoLCtg7KQBvqzxXi1YoTbOC5h5cBa8d-CtXbAzhpHDBXtpjbGcNeV7i3xioGLFBSM4BHRzPcbGCMMihGSMQiiU4AGkVAs2vIBPXPgZGwEeBZ_dzIPjbL7hpIcJLVEZJdhDC0ZvuKKd9hPFsCJtLWi_YJbAe1rqSTTCWQH8XHgDApTRHlasLTjOUfZyRm_ooWy2KQmvldim1KZ9_zN0-TSKokhFQUVWWO1RZY5nj7kpTpKgkCdfTKMqOqLf9Ko7BcvQc-3QPfw95bK_Ddl3Wp0WHfIxTONeF6x2pTotq-d4Frp2s3LB1PUwsJGGfvve9lTRsKrKqR225bHLIa1f-rAKWTqUoVn9Fg-s_RYaOpzrE3UXvV9-Y8DzVhcmvx_biRJP_FugX22ys-wRdiKifvoDdiuYp1EEAAA.Bv1-RluG8IIKsuu1ua_mU1l1hVMH8jBxBy34WIDelOeln4m121TpD9x2nL2vxQIZHMubwBVeiwi_m0kUjYZD6A"))
                .build());
        return parameters;
    }

    /* ************************** 系统管理 - system *********************************/

    @Bean
    public Docket accountApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("系统管理-账期管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizbasis.account.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket proxyApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("系统管理-代理人设定")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.proxy.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }
     @Bean
        public Docket batchInfoApi() {
            return new Docket(DocumentationType.SWAGGER_2)
                    .groupName("系统管理-批次信息管理")
                    .apiInfo(apiInfo())
                    .select()
                    .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizbasis.batch.controller"))
                    .paths(PathSelectors.any())
                    .build()
                    .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                    .globalRequestParameters(requestParameter());
        }

    @Bean
    public Docket applyApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储管理-申请")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizdomain.apply.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket authApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-用户与角色管理")
                .apiInfo(apiInfo())
                .select()
                .apis(
                basePackage(
                    "com.inossem.wms.system.auth.controller," +
                    "com.inossem.wms.bizbasis.masterdata.user.controller"
                ))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket slingApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("主数据管理-吊带管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizbasis.masterdata.car"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket commonApi() {
        String path = "com.inossem.wms.bizbasis.common.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-公共方法管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket emailApi() {
        String path = "com.inossem.wms.system.email.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-EMAIL管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket fileApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-附件管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.file.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket iotApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-通道门管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizbasis.masterdata.iot.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket metadataApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-元数据管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.metadata.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket i18nApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-国际化管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.system.i18n.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket jobApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-定时任务管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.job.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket logAPI() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-日志管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.system.log.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket materialDataAPI() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-物料主数据管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizbasis.masterdata.material.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket WhMaterialsDataAPI() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-仓库物料主数据管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizbasis.wh.material.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }


    @Bean
    public Docket noticeApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-消息管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.announcement.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket orgAPI() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-组织架构管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.bizbasis.masterdata.org.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket printAPI() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-打印管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.print.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket rocketMqApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-MQ管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.rocketmq.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket smsApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-SMS管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.sms.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket printTemplateApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-打印模板管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.print.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket wcsDataApi() {
        String path = "com.inossem.wms.bizbasis.masterdata.wcs.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-WCS主数据管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket toDoApi() {
        String path = "com.inossem.wms.bizbasis.todo.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("系统管理-待办管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    /* **************************审批管理*********************************/

    @Bean
    public Docket workflowApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("工作流管理-审批管理")
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.inossem.wms.system.workflow.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    /* **************************业务功能*********************************/

    @Bean
    public Docket arrangeApi() {
        String path = "com.inossem.wms.bizdomain.arrange.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-仓库整理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket scrapApi() {
        String path = "com.inossem.wms.bizdomain.apply.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-报废管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket inputApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-入库管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.input.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket inspectApi() {
        String path = "com.inossem.wms.bizdomain.inspect.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-验收管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket outputApi() {
        String path = "com.inossem.wms.bizdomain.output.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-出库管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket reportApi() {
        String path = "com.inossem.wms.bizdomain.report.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-报表管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket returnsApi() {
        String path = "com.inossem.wms.bizdomain.returns.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-退库管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket screenApi() {
        String path = "com.inossem.wms.bizdomain.screen.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-大屏管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket stocktakingApi() {
        String path = "com.inossem.wms.bizdomain.stocktaking.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-盘点管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket taskApi() {
        String path = "com.inossem.wms.bizdomain.task.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-上下架管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket transportApi() {
        String path = "com.inossem.wms.bizdomain.transport.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-转储管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket wcsApi() {
        String path = "com.inossem.wms.bizdomain.wcs.controller";
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-WCS管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage(path))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket deliveryApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-送货管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.delivery.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket electronicScaleApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-电子秤")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizbasis.electronicscale.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket collectionTaskApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-采集任务")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.collection.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket ToolManageApplyApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-工器具管理-申请模块")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.apply.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket ToolManageRegisterApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-工器具管理-登记")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.register.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket ToolManageCommonApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-工器具管理-公共方法模块")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.tool.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket InconformityApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-验收管理-不符合项")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.inconformity.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket LifetimeApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-寿期管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.lifetime.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket MaintainApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-维保管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.maintain.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket UnitizedApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(enabled)
                .groupName("仓储功能-成套设备")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.unitized.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket BizReceiptPaperController() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("图纸管理")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.paper.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    @Bean
    public Docket BizReceiptPaperSyncFileController() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("DTS同步日志")
                .apiInfo(apiInfo())
                .select()
                .apis(basePackage("com.inossem.wms.bizdomain.paper.controller"))
                .paths(PathSelectors.any())
                .build()
                .ignoredParameterTypes(BizContext.class, CurrentUser.class)
                .globalRequestParameters(requestParameter());
    }

    /**
     * 解决Spring Boot 2.6+版本与Swagger的兼容性问题
     * 添加静态资源处理器
     */
    @Bean
    @ConditionalOnMissingBean
    public WebMvcConfigurer webMvcConfigurer() {
        return new WebMvcConfigurer() {
            @Override
            public void addResourceHandlers(ResourceHandlerRegistry registry) {
                registry.addResourceHandler("/swagger-ui/**")
                        .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                        .resourceChain(false);
            }
        };
    }
}
