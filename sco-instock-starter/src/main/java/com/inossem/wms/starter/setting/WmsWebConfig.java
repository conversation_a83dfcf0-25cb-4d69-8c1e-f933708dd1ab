package com.inossem.wms.starter.setting;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.inossem.wms.common.constant.Const;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.inossem.wms.common.util.UtilLocalDateTime;
import com.inossem.wms.starter.interceptor.WmsBizContextArgumentResolver;
import com.inossem.wms.starter.interceptor.WmsHttpResultInterceptor;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class WmsWebConfig implements WebMvcConfigurer {

    @Autowired
    private WmsHttpResultInterceptor httpResultInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 拦截器配置注册开始..");
        registry.addInterceptor(httpResultInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    "/swagger-resources/**",
                    "/webjars/**",
                    "/v2/**",
                    "/doc.html",
                    "/swagger-ui.html",
                    "/swagger-ui/**",
                    "/favicon.ico"
                );
        WebMvcConfigurer.super.addInterceptors(registry);
        log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 拦截器配置注册完成");
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new WmsBizContextArgumentResolver());
    }

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 设置允许跨域的路径
        log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 跨域路径映射配置开始..");
        registry.addMapping("/**")
            // 设置允许跨域请求的域名
            .allowedOriginPatterns("*")
            // 是否允许证书 不再默认开启
            .allowCredentials(true)
            // 设置允许的方法
            .allowedMethods("*")
            // 跨域允许时间
            .maxAge(3600);
        log.info(UtilLocalDateTime.getStringDateTimeForLocalDateTime(UtilLocalDateTime.getNow()) + " 跨域路径映射配置完成");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(0, stringHttpMessageConverter());
        converters.add(1, fastJsonHttpMessageConverter());
    }

    @Bean
    public StringHttpMessageConverter stringHttpMessageConverter() {
        return new StringHttpMessageConverter();
    }

    @Bean
    public FastJsonHttpMessageConverter fastJsonHttpMessageConverter() {

        // 日期java.util.Date返回值null时改为""
        ValueFilter dateFilter = (Object object, String name, Object value) -> {
            try {
                if (value == null && object != null) {
                    for (Class<?> clazz = object.getClass(); clazz != Object.class; clazz = clazz.getSuperclass()) {
                        try {
                            if (Date.class.isAssignableFrom(clazz.getDeclaredField(name).getType())) {
                                return Const.STRING_EMPTY;
                            }
                        } catch (Exception ignored) {

                        }
                    }
                }
            } catch (Exception ignored) {

            }
            return value;
        };
        // 创建FastJson信息转换对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        // 创建FastJsonConfig对象并设定序列化规则
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(
                // 生产环境不要加PrettyFormat 影响性能
                // SerializerFeature.PrettyFormat,
                // 禁止循环引用
                SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteDateUseDateFormat,
                SerializerFeature.SortField,
                // SerializerFeature.IgnoreNonFieldGetter
                SerializerFeature.WriteNullBooleanAsFalse,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteNullNumberAsZero,
                SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteEnumUsingToString,
                SerializerFeature.WriteNonStringKeyAsString);

        fastJsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        fastJsonConfig.setSerializeFilters(dateFilter);

        // 解决中文乱码问题
        List<MediaType> fastMediaTypes = new ArrayList<>();
        // 设定Json格式且编码为utf-8
        fastMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        fastConverter.setSupportedMediaTypes(fastMediaTypes);
        // 将转换规则应用于转换对象
        fastConverter.setFastJsonConfig(fastJsonConfig);
        return fastConverter;
    }



}
